export class PriceTrackerMarketAnalysisComponent {
  constructor(container) {
    this.container = container;
    this.priceData = [];
    this.marketData = {};
    this.filteredData = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'totalMarketValue';
    this.sortDirection = 'desc';
    this.dateRange = {};
    this.selectedTimeframe = 'monthly'; // monthly, quarterly, yearly
  }

  async init(priceData, dateRange) {
    console.log('Initializing Market Analysis component with', priceData.length, 'records');
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    
    // Process data for market analysis
    this.processMarketAnalysis();
    this.applyFilters();
    this.calculateTotalPages();
    
    // Render the analysis
    this.render();
  }

  processMarketAnalysis() {
    console.log('Processing market analysis data...');
    
    // Group data by inventory ID for market analysis
    const inventoryGroups = {};
    
    this.priceData.forEach(item => {
      const inventoryId = item.inventoryId;
      if (!inventoryGroups[inventoryId]) {
        inventoryGroups[inventoryId] = {
          inventoryId: inventoryId,
          description: item.description || 'N/A',
          vendors: new Set(),
          totalPurchases: 0,
          totalValue: 0,
          priceHistory: [],
          monthlyData: {},
          quarterlyData: {},
          yearlyData: {},
          priceVolatility: 0,
          marketShare: {},
          competitiveIndex: 0,
          demandTrend: 'stable',
          supplyRisk: 'low'
        };
      }
      
      const group = inventoryGroups[inventoryId];
      group.vendors.add(item.vendorName);
      group.totalPurchases += 1;
      group.totalValue += (item.unitCost || 0) * (item.quantity || 1);
      
      // Add to price history
      group.priceHistory.push({
        date: item.purchaseDate,
        price: item.unitCost || 0,
        vendor: item.vendorName,
        quantity: item.quantity || 1,
        currency: item.currency || 'CAD'
      });
      
      // Group by time periods
      const date = new Date(item.purchaseDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const quarterKey = `${date.getFullYear()}-Q${Math.ceil((date.getMonth() + 1) / 3)}`;
      const yearKey = date.getFullYear().toString();
      
      // Monthly data
      if (!group.monthlyData[monthKey]) {
        group.monthlyData[monthKey] = { purchases: 0, totalValue: 0, avgPrice: 0, vendors: new Set() };
      }
      group.monthlyData[monthKey].purchases += 1;
      group.monthlyData[monthKey].totalValue += (item.unitCost || 0) * (item.quantity || 1);
      group.monthlyData[monthKey].vendors.add(item.vendorName);
      
      // Quarterly data
      if (!group.quarterlyData[quarterKey]) {
        group.quarterlyData[quarterKey] = { purchases: 0, totalValue: 0, avgPrice: 0, vendors: new Set() };
      }
      group.quarterlyData[quarterKey].purchases += 1;
      group.quarterlyData[quarterKey].totalValue += (item.unitCost || 0) * (item.quantity || 1);
      group.quarterlyData[quarterKey].vendors.add(item.vendorName);
      
      // Yearly data
      if (!group.yearlyData[yearKey]) {
        group.yearlyData[yearKey] = { purchases: 0, totalValue: 0, avgPrice: 0, vendors: new Set() };
      }
      group.yearlyData[yearKey].purchases += 1;
      group.yearlyData[yearKey].totalValue += (item.unitCost || 0) * (item.quantity || 1);
      group.yearlyData[yearKey].vendors.add(item.vendorName);
    });
    
    // Calculate market metrics for each inventory item
    Object.values(inventoryGroups).forEach(group => {
      // Sort price history by date
      group.priceHistory.sort((a, b) => new Date(a.date) - new Date(b.date));
      
      // Calculate price volatility (standard deviation of prices)
      const prices = group.priceHistory.map(p => p.price);
      if (prices.length > 1) {
        const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
        const variance = prices.reduce((sum, price) => sum + Math.pow(price - avgPrice, 2), 0) / prices.length;
        group.priceVolatility = Math.sqrt(variance);
      }
      
      // Calculate average prices for time periods
      Object.keys(group.monthlyData).forEach(monthKey => {
        const monthData = group.monthlyData[monthKey];
        monthData.avgPrice = monthData.purchases > 0 ? monthData.totalValue / monthData.purchases : 0;
        monthData.vendorCount = monthData.vendors.size;
      });
      
      Object.keys(group.quarterlyData).forEach(quarterKey => {
        const quarterData = group.quarterlyData[quarterKey];
        quarterData.avgPrice = quarterData.purchases > 0 ? quarterData.totalValue / quarterData.purchases : 0;
        quarterData.vendorCount = quarterData.vendors.size;
      });
      
      Object.keys(group.yearlyData).forEach(yearKey => {
        const yearData = group.yearlyData[yearKey];
        yearData.avgPrice = yearData.purchases > 0 ? yearData.totalValue / yearData.purchases : 0;
        yearData.vendorCount = yearData.vendors.size;
      });
      
      // Calculate market share by vendor
      const vendorSpend = {};
      group.priceHistory.forEach(purchase => {
        const vendor = purchase.vendor;
        const value = purchase.price * purchase.quantity;
        vendorSpend[vendor] = (vendorSpend[vendor] || 0) + value;
      });
      
      Object.keys(vendorSpend).forEach(vendor => {
        group.marketShare[vendor] = (vendorSpend[vendor] / group.totalValue) * 100;
      });
      
      // Calculate competitive index (number of vendors / price volatility)
      group.competitiveIndex = group.vendors.size / Math.max(group.priceVolatility, 1);
      
      // Determine demand trend based on purchase frequency over time
      const recentMonths = Object.keys(group.monthlyData).sort().slice(-6);
      const oldMonths = Object.keys(group.monthlyData).sort().slice(0, 6);
      
      if (recentMonths.length > 0 && oldMonths.length > 0) {
        const recentAvg = recentMonths.reduce((sum, month) => sum + group.monthlyData[month].purchases, 0) / recentMonths.length;
        const oldAvg = oldMonths.reduce((sum, month) => sum + group.monthlyData[month].purchases, 0) / oldMonths.length;
        
        if (recentAvg > oldAvg * 1.2) {
          group.demandTrend = 'increasing';
        } else if (recentAvg < oldAvg * 0.8) {
          group.demandTrend = 'decreasing';
        } else {
          group.demandTrend = 'stable';
        }
      }
      
      // Determine supply risk based on vendor concentration
      const topVendorShare = Math.max(...Object.values(group.marketShare));
      if (topVendorShare > 80) {
        group.supplyRisk = 'high';
      } else if (topVendorShare > 60) {
        group.supplyRisk = 'medium';
      } else {
        group.supplyRisk = 'low';
      }
      
      // Convert vendor count to number
      group.vendorCount = group.vendors.size;
      group.totalMarketValue = group.totalValue;
    });
    
    this.marketData = inventoryGroups;
    console.log('Market analysis processed:', Object.keys(this.marketData).length, 'inventory items');
  }

  applyFilters() {
    let items = Object.values(this.marketData);
    
    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      items = items.filter(item =>
        item.inventoryId.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply sorting
    items.sort((a, b) => {
      let aValue, bValue;
      
      switch (this.sortField) {
        case 'inventoryId':
          aValue = a.inventoryId;
          bValue = b.inventoryId;
          break;
        case 'totalMarketValue':
          aValue = a.totalMarketValue;
          bValue = b.totalMarketValue;
          break;
        case 'vendorCount':
          aValue = a.vendorCount;
          bValue = b.vendorCount;
          break;
        case 'priceVolatility':
          aValue = a.priceVolatility;
          bValue = b.priceVolatility;
          break;
        case 'competitiveIndex':
          aValue = a.competitiveIndex;
          bValue = b.competitiveIndex;
          break;
        default:
          aValue = a.totalMarketValue;
          bValue = b.totalMarketValue;
      }
      
      if (typeof aValue === 'string') {
        return this.sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
    });
    
    this.filteredData = items;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredData.length / this.itemsPerPage));
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    // Check if container already has the modal, if not create it
    if (!this.container.querySelector('#market-detail-modal')) {
      this.container.innerHTML = `
        <div class="market-analysis-container">
          <div id="market-table-container"></div>
          ${this.renderModal()}
        </div>
      `;
      this.setupModalListeners();
    }
    
    // Only update the table content
    const tableContainer = this.container.querySelector('#market-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = this.renderTable();
      this.setupTableEventListeners();
    }
  }

  renderTable() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
      return this.renderNoDataState();
    }
    
    return `
      <!-- Market Analysis Table -->
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="inventoryId">
                  Inventory ID
                  <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="totalMarketValue">
                  Market Value
                  <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="vendorCount">
                  Vendors
                  <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="priceVolatility">
                  Volatility
                  <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Demand Trend</th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Supply Risk</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${pageData.map(item => this.renderTableRow(item)).join('')}
            </tbody>
          </table>
        </div>
        
        ${this.renderPagination()}
      </div>
    `;
  }

  renderTableRow(item) {
    const demandTrendClass = {
      'increasing': 'text-green-600 dark:text-green-400',
      'decreasing': 'text-red-600 dark:text-red-400',
      'stable': 'text-gray-600 dark:text-gray-400'
    }[item.demandTrend] || 'text-gray-600 dark:text-gray-400';

    const supplyRiskClass = {
      'high': 'text-red-600 dark:text-red-400',
      'medium': 'text-yellow-600 dark:text-yellow-400',
      'low': 'text-green-600 dark:text-green-400'
    }[item.supplyRisk] || 'text-gray-600 dark:text-gray-400';

    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(item.inventoryId)}</div>
        </td>
        <td class="px-3 py-4">
          <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">${this.escapeHtml(item.description)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900 dark:text-white">${this.formatCurrency(item.totalMarketValue)}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">${item.totalPurchases} purchases</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${item.vendorCount}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${this.formatCurrency(item.priceVolatility)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${demandTrendClass} bg-gray-100 dark:bg-gray-800">
            ${item.demandTrend}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${supplyRiskClass} bg-gray-100 dark:bg-gray-800">
            ${item.supplyRisk}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button class="view-market-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-inventory="${this.escapeHtml(item.inventoryId)}" title="View Market Details">
            <i class="fas fa-chart-line"></i>
          </button>
        </td>
      </tr>
    `;
  }

  renderPagination() {
    if (this.totalPages <= 1) return '';

    return `
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 px-4 py-3 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredData.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredData.length)} of
          ${this.filteredData.length} items
        </div>

        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}" ${this.currentPage === 1 ? 'disabled' : ''}>
            First
          </button>
          <button id="prev-page" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}" ${this.currentPage === 1 ? 'disabled' : ''}>
            Previous
          </button>
          <span class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Page ${this.currentPage} of ${this.totalPages}
          </span>
          <button id="next-page" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : ''}" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            Next
          </button>
          <button id="last-page" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : ''}" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            Last
          </button>
        </div>
      </div>
    `;
  }

  renderModal() {
    return `
      <!-- Market Detail Modal -->
      <div id="market-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-2 sm:p-4">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] flex flex-col">
            <!-- Fixed Header -->
            <div class="flex-shrink-0 px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-center min-h-[2rem]">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate pr-4" id="modal-market-title">Market Analysis Details</h3>
                <button id="close-market-modal" class="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            <!-- Scrollable Content -->
            <div id="modal-market-content" class="flex-1 p-4 sm:p-6 overflow-y-auto min-h-0">
              <!-- Content will be populated dynamically -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderNoDataState() {
    return `
      <div class="flex flex-col items-center justify-center p-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Market Data Available</h3>
        <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
          ${this.searchTerm ?
            'No items match your search criteria. Try adjusting your search terms.' :
            'No market analysis data is available. Please ensure price data has been loaded.'}
        </p>
        ${this.searchTerm ? `
          <button id="clear-search-market" class="mt-4 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors">
            Clear Search
          </button>
        ` : ''}
      </div>
    `;
  }

  setupTableEventListeners() {
    // Pagination buttons
    const firstPageBtn = this.container.querySelector('#first-page');
    if (firstPageBtn && !firstPageBtn.disabled) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.render();
      });
    }

    const prevPageBtn = this.container.querySelector('#prev-page');
    if (prevPageBtn && !prevPageBtn.disabled) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#next-page');
    if (nextPageBtn && !nextPageBtn.disabled) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = this.container.querySelector('#last-page');
    if (lastPageBtn && !lastPageBtn.disabled) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.render();
      });
    }

    // Sorting
    const sortHeaders = this.container.querySelectorAll('[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'desc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Market detail buttons
    const marketDetailButtons = this.container.querySelectorAll('.view-market-details');
    marketDetailButtons.forEach(btn => {
      if (!btn.hasAttribute('data-listener-added')) {
        btn.setAttribute('data-listener-added', 'true');
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          const inventoryId = e.target.closest('button').getAttribute('data-inventory');
          this.showMarketDetailModal(inventoryId);
        });
      }
    });

    // Clear search button (in no data state)
    const clearSearchBtn = this.container.querySelector('#clear-search-market');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        this.currentPage = 1;
        this.applyFilters();
        this.calculateTotalPages();
        this.render();
      });
    }
  }

  setupModalListeners() {
    const modal = this.container.querySelector('#market-detail-modal');
    const closeBtn = this.container.querySelector('#close-market-modal');

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
        }
      });
    }
  }

  showMarketDetailModal(inventoryId) {
    const item = this.marketData[inventoryId];
    if (!item) return;

    const modal = this.container.querySelector('#market-detail-modal');
    const title = this.container.querySelector('#modal-market-title');
    const content = this.container.querySelector('#modal-market-content');

    title.textContent = `${inventoryId} - Market Analysis`;
    content.innerHTML = this.renderMarketDetailContent(item);

    modal.classList.remove('hidden');
  }

  renderMarketDetailContent(item) {
    // Get time series data based on selected timeframe
    const timeData = this.selectedTimeframe === 'monthly' ? item.monthlyData :
                     this.selectedTimeframe === 'quarterly' ? item.quarterlyData :
                     item.yearlyData;

    const timeKeys = Object.keys(timeData).sort();

    // Get top vendors by market share
    const topVendors = Object.entries(item.marketShare)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    return `
      <div class="space-y-6 max-h-full">
        <!-- Market Overview Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 flex-shrink-0">
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${this.formatCurrency(item.totalMarketValue)}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Market Value</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${item.vendorCount}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Active Vendors</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${item.totalPurchases}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Purchases</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${this.formatCurrency(item.priceVolatility)}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Price Volatility</div>
          </div>
        </div>

        <!-- Market Trends -->
        <div class="flex-shrink-0">
          <div class="flex justify-between items-center mb-3">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white">Market Trends</h4>
            <select id="timeframe-selector" class="px-3 py-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="monthly" ${this.selectedTimeframe === 'monthly' ? 'selected' : ''}>Monthly</option>
              <option value="quarterly" ${this.selectedTimeframe === 'quarterly' ? 'selected' : ''}>Quarterly</option>
              <option value="yearly" ${this.selectedTimeframe === 'yearly' ? 'selected' : ''}>Yearly</option>
            </select>
          </div>
          <div class="overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg max-h-80 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Period</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Purchases</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Value</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Avg Price</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Vendors</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${timeKeys.map(timeKey => {
                  const data = timeData[timeKey];
                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${timeKey}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${data.purchases}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(data.totalValue)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(data.avgPrice)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${data.vendorCount}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Vendor Market Share -->
        <div class="flex-1 min-h-0">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Vendor Market Share</h4>
          <div class="overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Vendor</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Market Share</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Share Value</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${topVendors.map(([vendor, share]) => {
                  const shareValue = (item.totalMarketValue * share) / 100;
                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(vendor)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">
                        <div class="flex items-center">
                          <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${Math.min(share, 100)}%"></div>
                          </div>
                          <span>${share.toFixed(1)}%</span>
                        </div>
                      </td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(shareValue)}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Market Intelligence Summary -->
        <div class="flex-shrink-0 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Market Intelligence</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-700 dark:text-gray-300">Demand Trend:</span>
              <span class="ml-2 px-2 py-1 rounded text-xs font-medium ${this.getDemandTrendClass(item.demandTrend)}">${item.demandTrend}</span>
            </div>
            <div>
              <span class="font-medium text-gray-700 dark:text-gray-300">Supply Risk:</span>
              <span class="ml-2 px-2 py-1 rounded text-xs font-medium ${this.getSupplyRiskClass(item.supplyRisk)}">${item.supplyRisk}</span>
            </div>
            <div>
              <span class="font-medium text-gray-700 dark:text-gray-300">Competitive Index:</span>
              <span class="ml-2 text-gray-900 dark:text-white">${item.competitiveIndex.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  getDemandTrendClass(trend) {
    const classes = {
      'increasing': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
      'decreasing': 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
      'stable': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
    };
    return classes[trend] || classes['stable'];
  }

  getSupplyRiskClass(risk) {
    const classes = {
      'high': 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
      'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
      'low': 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    };
    return classes[risk] || classes['low'];
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    this.currentPage = 1;
    this.applyFilters();
    this.calculateTotalPages();
    this.render();
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processMarketAnalysis();
      this.applyFilters();
      this.calculateTotalPages();
      this.render();
    } else {
      this.render();
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}
