export class PriceTrackerMarketAnalysisComponent {
  constructor(container) {
    this.container = container;
    this.priceData = [];
    this.companyAnalysis = {
      yearlyData: {},
      monthlyData: {},
      totalSpending: 0,
      totalPurchases: 0,
      activeVendors: new Set(),
      activeParts: new Set(),
      trends: {}
    };
    this.searchTerm = '';
    this.selectedView = 'yearly'; // yearly, monthly
    this.chartInstances = new Map();
  }

  async init(priceData, dateRange) {
    console.log('Initializing Company Market Analysis with', priceData.length, 'records');
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};

    // Load Chart.js library
    await this.loadChartLibrary();

    // Process data for company-wide analysis
    this.processCompanyAnalysis();

    // Render the analysis
    this.render();
  }

  async loadChartLibrary() {
    return new Promise((resolve, reject) => {
      if (window.ApexCharts) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '@kpi/library/apexcharts.min.js';
      script.onload = () => {
        console.log('ApexCharts loaded successfully');
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load ApexCharts');
        reject(new Error('Failed to load ApexCharts'));
      };
      document.head.appendChild(script);
    });
  }

  processCompanyAnalysis() {
    console.log('Processing company-wide market analysis...');

    // Reset analysis data
    this.companyAnalysis = {
      yearlyData: {},
      monthlyData: {},
      totalSpending: 0,
      totalPurchases: 0,
      activeVendors: new Set(),
      activeParts: new Set(),
      trends: {}
    };

    // Process all purchase data for company-wide analysis
    this.priceData.forEach(item => {
      const date = new Date(item.purchaseDate);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 1-12
      const monthKey = `${year}-${String(month).padStart(2, '0')}`;
      const purchaseValue = (item.unitCost || 0) * (item.quantity || 1);

      // Track company totals
      this.companyAnalysis.totalSpending += purchaseValue;
      this.companyAnalysis.totalPurchases += 1;
      this.companyAnalysis.activeVendors.add(item.vendorName);
      this.companyAnalysis.activeParts.add(item.inventoryId);

      // Yearly aggregation
      if (!this.companyAnalysis.yearlyData[year]) {
        this.companyAnalysis.yearlyData[year] = {
          year: year,
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set(),
          monthlyBreakdown: {}
        };
      }

      const yearData = this.companyAnalysis.yearlyData[year];
      yearData.totalSpending += purchaseValue;
      yearData.totalPurchases += 1;
      yearData.vendors.add(item.vendorName);
      yearData.parts.add(item.inventoryId);

      // Monthly breakdown within year
      if (!yearData.monthlyBreakdown[month]) {
        yearData.monthlyBreakdown[month] = {
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set()
        };
      }

      yearData.monthlyBreakdown[month].totalSpending += purchaseValue;
      yearData.monthlyBreakdown[month].totalPurchases += 1;
      yearData.monthlyBreakdown[month].vendors.add(item.vendorName);
      yearData.monthlyBreakdown[month].parts.add(item.inventoryId);

      // Global monthly data
      if (!this.companyAnalysis.monthlyData[monthKey]) {
        this.companyAnalysis.monthlyData[monthKey] = {
          monthKey: monthKey,
          year: year,
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set()
        };
      }

      this.companyAnalysis.monthlyData[monthKey].totalSpending += purchaseValue;
      this.companyAnalysis.monthlyData[monthKey].totalPurchases += 1;
      this.companyAnalysis.monthlyData[monthKey].vendors.add(item.vendorName);
      this.companyAnalysis.monthlyData[monthKey].parts.add(item.inventoryId);
    });

    // Calculate percentage changes and trends
    this.calculateTrends();

    // Convert Sets to counts for final data
    Object.values(this.companyAnalysis.yearlyData).forEach(yearData => {
      yearData.vendorCount = yearData.vendors.size;
      yearData.partCount = yearData.parts.size;

      Object.values(yearData.monthlyBreakdown).forEach(monthData => {
        monthData.vendorCount = monthData.vendors.size;
        monthData.partCount = monthData.parts.size;
      });
    });

    Object.values(this.companyAnalysis.monthlyData).forEach(monthData => {
      monthData.vendorCount = monthData.vendors.size;
      monthData.partCount = monthData.parts.size;
    });

    this.companyAnalysis.totalVendors = this.companyAnalysis.activeVendors.size;
    this.companyAnalysis.totalParts = this.companyAnalysis.activeParts.size;

    console.log('Company analysis processed:', {
      totalSpending: this.formatCurrency(this.companyAnalysis.totalSpending),
      totalPurchases: this.companyAnalysis.totalPurchases,
      totalVendors: this.companyAnalysis.totalVendors,
      totalParts: this.companyAnalysis.totalParts,
      years: Object.keys(this.companyAnalysis.yearlyData).length
    });
  }

  calculateTrends() {
    // Calculate year-over-year changes
    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();

    for (let i = 1; i < years.length; i++) {
      const currentYear = years[i];
      const previousYear = years[i - 1];

      const currentData = this.companyAnalysis.yearlyData[currentYear];
      const previousData = this.companyAnalysis.yearlyData[previousYear];

      if (previousData.totalSpending > 0) {
        currentData.spendingGrowth = ((currentData.totalSpending - previousData.totalSpending) / previousData.totalSpending) * 100;
      } else {
        currentData.spendingGrowth = 0;
      }

      if (previousData.totalPurchases > 0) {
        currentData.purchaseGrowth = ((currentData.totalPurchases - previousData.totalPurchases) / previousData.totalPurchases) * 100;
      } else {
        currentData.purchaseGrowth = 0;
      }
    }

    // Calculate month-over-month changes for current year
    const currentYear = Math.max(...years);
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (currentYearData) {
      const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();

      for (let i = 1; i < months.length; i++) {
        const currentMonth = months[i];
        const previousMonth = months[i - 1];

        const currentMonthData = currentYearData.monthlyBreakdown[currentMonth];
        const previousMonthData = currentYearData.monthlyBreakdown[previousMonth];

        if (previousMonthData.totalSpending > 0) {
          currentMonthData.spendingGrowth = ((currentMonthData.totalSpending - previousMonthData.totalSpending) / previousMonthData.totalSpending) * 100;
        } else {
          currentMonthData.spendingGrowth = 0;
        }
      }
    }
  }

  getMonthName(monthNumber) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthNumber - 1] || 'Unknown';
  }

  render() {
    this.container.innerHTML = `
      <div class="market-analysis-dashboard">
        <!-- Executive Summary Cards -->
        ${this.renderExecutiveSummary()}

        <!-- View Toggle -->
        ${this.renderViewToggle()}

        <!-- Main Analysis Content -->
        <div id="analysis-content">
          ${this.selectedView === 'yearly' ? this.renderYearlyAnalysis() : this.renderMonthlyAnalysis()}
        </div>
      </div>
    `;

    this.setupEventListeners();

    // Render charts after DOM is ready
    setTimeout(() => {
      this.renderCharts();
    }, 100);
  }

  renderExecutiveSummary() {
    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];
    const previousYearData = this.companyAnalysis.yearlyData[currentYear - 1];

    let yearOverYearGrowth = 0;
    if (currentYearData && previousYearData && previousYearData.totalSpending > 0) {
      yearOverYearGrowth = ((currentYearData.totalSpending - previousYearData.totalSpending) / previousYearData.totalSpending) * 100;
    }

    return `
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Total Company Spending -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Spending</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">${this.formatCurrency(this.companyAnalysis.totalSpending)}</p>
            </div>
          </div>
        </div>

        <!-- Year-over-Year Growth -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 ${yearOverYearGrowth >= 0 ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'} rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 ${yearOverYearGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${yearOverYearGrowth >= 0 ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'}"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">YoY Growth</p>
              <p class="text-2xl font-semibold ${yearOverYearGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
                ${yearOverYearGrowth >= 0 ? '+' : ''}${yearOverYearGrowth.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <!-- Active Vendors -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Vendors</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">${this.companyAnalysis.totalVendors}</p>
            </div>
          </div>
        </div>

        <!-- Total Purchases -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Purchases</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">${this.companyAnalysis.totalPurchases.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderViewToggle() {
    return `
      <div class="flex justify-center mb-6">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button id="yearly-view" class="px-4 py-2 text-sm font-medium rounded-md transition-colors ${this.selectedView === 'yearly' ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}">
            Yearly Analysis
          </button>
          <button id="monthly-view" class="px-4 py-2 text-sm font-medium rounded-md transition-colors ${this.selectedView === 'monthly' ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}">
            Monthly Analysis
          </button>
        </div>
      </div>
    `;
  }

  renderYearlyAnalysis() {
    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();

    return `
      <div class="space-y-8">
        <!-- Yearly Spending Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div id="yearly-spending-chart"></div>
        </div>

        <!-- Yearly Data Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Yearly Performance Summary</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Spending</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Purchases</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendors</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Parts</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg per Purchase</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${years.map(year => {
                  const yearData = this.companyAnalysis.yearlyData[year];
                  const avgPerPurchase = yearData.totalPurchases > 0 ? yearData.totalSpending / yearData.totalPurchases : 0;
                  const growthClass = (yearData.spendingGrowth || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" data-year="${year}">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">${year}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(yearData.totalSpending)}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${growthClass}">
                        ${yearData.spendingGrowth ? (yearData.spendingGrowth >= 0 ? '+' : '') + yearData.spendingGrowth.toFixed(1) + '%' : '—'}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.totalPurchases.toLocaleString()}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.vendorCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.partCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(avgPerPurchase)}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderMonthlyAnalysis() {
    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (!currentYearData) {
      return `
        <div class="text-center py-12">
          <p class="text-gray-500 dark:text-gray-400">No data available for ${currentYear}</p>
        </div>
      `;
    }

    const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();

    return `
      <div class="space-y-8">
        <!-- Monthly Spending Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div id="monthly-spending-chart"></div>
        </div>

        <!-- Monthly Data Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Monthly Performance - ${currentYear}</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Month</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Spending</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Purchases</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendors</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Parts</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg per Purchase</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${months.map(month => {
                  const monthData = currentYearData.monthlyBreakdown[month];
                  const avgPerPurchase = monthData.totalPurchases > 0 ? monthData.totalSpending / monthData.totalPurchases : 0;
                  const growthClass = (monthData.spendingGrowth || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">${monthData.monthName}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.totalSpending)}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${growthClass}">
                        ${monthData.spendingGrowth ? (monthData.spendingGrowth >= 0 ? '+' : '') + monthData.spendingGrowth.toFixed(1) + '%' : '—'}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.totalPurchases.toLocaleString()}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.vendorCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.partCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(avgPerPurchase)}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderCharts() {
    if (this.selectedView === 'yearly') {
      this.renderYearlyChart();
    } else {
      this.renderMonthlyChart();
    }
  }

  renderYearlyChart() {
    const chartContainer = document.getElementById('yearly-spending-chart');
    if (!chartContainer || !window.ApexCharts) return;

    // Destroy existing chart
    if (this.chartInstances.has('yearly')) {
      this.chartInstances.get('yearly').destroy();
    }

    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();
    const spendingData = years.map(year => this.companyAnalysis.yearlyData[year].totalSpending);
    const growthData = years.map(year => this.companyAnalysis.yearlyData[year].spendingGrowth || 0);

    const options = {
      series: [{
        name: 'Annual Spending',
        type: 'column',
        data: spendingData
      }, {
        name: 'Growth %',
        type: 'line',
        data: growthData
      }],
      chart: {
        height: 350,
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        }
      },
      stroke: {
        width: [0, 4]
      },
      title: {
        text: 'Annual Spending and Growth Trends',
        style: {
          fontSize: '16px',
          fontWeight: 600
        }
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [1],
        formatter: function(val) {
          return val.toFixed(1) + '%';
        }
      },
      labels: years,
      xaxis: {
        type: 'category',
        title: {
          text: 'Year'
        }
      },
      yaxis: [{
        title: {
          text: 'Spending (CAD)',
        },
        labels: {
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD',
              notation: 'compact'
            }).format(val);
          }
        }
      }, {
        opposite: true,
        title: {
          text: 'Growth %'
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          }
        }
      }],
      tooltip: {
        shared: true,
        intersect: false,
        y: [{
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD'
            }).format(val);
          }
        }, {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          }
        }]
      },
      colors: ['#3B82F6', '#10B981'],
      fill: {
        opacity: [0.85, 1]
      }
    };

    const chart = new ApexCharts(chartContainer, options);
    chart.render();
    this.chartInstances.set('yearly', chart);
  }

  renderMonthlyChart() {
    const chartContainer = document.getElementById('monthly-spending-chart');
    if (!chartContainer || !window.ApexCharts) return;

    // Destroy existing chart
    if (this.chartInstances.has('monthly')) {
      this.chartInstances.get('monthly').destroy();
    }

    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (!currentYearData) return;

    const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();
    const labels = months.map(month => this.getMonthName(month));
    const spendingData = months.map(month => currentYearData.monthlyBreakdown[month].totalSpending);
    const growthData = months.map(month => currentYearData.monthlyBreakdown[month].spendingGrowth || 0);

    const options = {
      series: [{
        name: 'Monthly Spending',
        type: 'column',
        data: spendingData
      }, {
        name: 'Growth %',
        type: 'line',
        data: growthData
      }],
      chart: {
        height: 350,
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        }
      },
      stroke: {
        width: [0, 4]
      },
      title: {
        text: `Monthly Spending and Growth Trends - ${currentYear}`,
        style: {
          fontSize: '16px',
          fontWeight: 600
        }
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [1],
        formatter: function(val) {
          return val.toFixed(1) + '%';
        }
      },
      labels: labels,
      xaxis: {
        type: 'category',
        title: {
          text: 'Month'
        }
      },
      yaxis: [{
        title: {
          text: 'Spending (CAD)',
        },
        labels: {
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD',
              notation: 'compact'
            }).format(val);
          }
        }
      }, {
        opposite: true,
        title: {
          text: 'Growth %'
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          }
        }
      }],
      tooltip: {
        shared: true,
        intersect: false,
        y: [{
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD'
            }).format(val);
          }
        }, {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          }
        }]
      },
      colors: ['#3B82F6', '#10B981'],
      fill: {
        opacity: [0.85, 1]
      }
    };

    const chart = new ApexCharts(chartContainer, options);
    chart.render();
    this.chartInstances.set('monthly', chart);
  }

  setupEventListeners() {
    // View toggle buttons
    const yearlyViewBtn = this.container.querySelector('#yearly-view');
    const monthlyViewBtn = this.container.querySelector('#monthly-view');

    if (yearlyViewBtn) {
      yearlyViewBtn.addEventListener('click', () => {
        if (this.selectedView !== 'yearly') {
          this.selectedView = 'yearly';
          this.render();
        }
      });
    }

    if (monthlyViewBtn) {
      monthlyViewBtn.addEventListener('click', () => {
        if (this.selectedView !== 'monthly') {
          this.selectedView = 'monthly';
          this.render();
        }
      });
    }

    // Year row click handlers for drilling down
    const yearRows = this.container.querySelectorAll('[data-year]');
    yearRows.forEach(row => {
      row.addEventListener('click', () => {
        const year = parseInt(row.getAttribute('data-year'));
        this.showYearDetails(year);
      });
    });
  }

  showYearDetails(year) {
    const yearData = this.companyAnalysis.yearlyData[year];
    if (!yearData) return;

    // Switch to monthly view and update the year
    this.selectedView = 'monthly';

    // For now, just switch to monthly view
    // In the future, we could add year selection functionality
    this.render();
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    // For executive dashboard, search is not applicable
    // Could be used to filter years or months in the future
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processCompanyAnalysis();
      this.render();
    } else {
      this.renderNoDataState();
    }
  }

  renderNoDataState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Market Data Available</h3>
        <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
          No purchasing data is available for market analysis. Please ensure price data has been loaded and try refreshing.
        </p>
      </div>
    `;
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  destroy() {
    // Clean up ApexCharts instances
    this.chartInstances.forEach((chart) => {
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
    });
    this.chartInstances.clear();

    if (this.container) {
      this.container.innerHTML = '';
    }
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    this.currentPage = 1;
    this.applyFilters();
    this.calculateTotalPages();
    this.render();
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processMarketAnalysis();
      this.applyFilters();
      this.calculateTotalPages();
      this.render();
    } else {
      this.render();
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}
