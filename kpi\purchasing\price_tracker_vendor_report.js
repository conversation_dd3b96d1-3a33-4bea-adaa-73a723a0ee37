// Price Tracker Vendor Report Component
// Detailed vendor price analysis with parts-based price tracking over time

export class PriceTrackerVendorReportComponent {
  constructor(container) {
    this.container = container;
    this.priceData = [];
    this.vendorAnalysis = [];
    this.filteredData = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'totalSpend';
    this.sortDirection = 'desc';
    this.selectedVendor = null;
    this.dateRange = null;
  }

  async init(priceData, dateRange) {
    console.log("Initializing Price Tracker Vendor Report");
    
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    
    if (this.priceData.length === 0) {
      this.renderNoDataState();
      return;
    }

    // Process data for vendor analysis
    this.processVendorAnalysis();
    this.applyFilters();
    this.calculateTotalPages();
    
    // Render the report
    this.render();
    
    // Setup event listeners
    this.setupEventListeners();
  }

  processVendorAnalysis() {
    console.log(`Processing ${this.priceData.length} price records for vendor analysis`);
    
    // Group by vendor for comprehensive analysis
    const vendorData = {};
    
    this.priceData.forEach(item => {
      const vendor = item.vendorName;
      const part = item.inventoryId;
      const date = new Date(item.purchaseDate);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 1-based month
      const yearMonth = `${year}-${month.toString().padStart(2, '0')}`;
      
      if (!vendorData[vendor]) {
        vendorData[vendor] = {
          vendorName: vendor,
          parts: {},
          totalSpend: 0,
          totalOrders: 0,
          partCount: 0,
          priceHistory: {},
          firstPurchase: date,
          lastPurchase: date,
          avgOrderValue: 0
        };
      }
      
      const vendorInfo = vendorData[vendor];
      
      // Update vendor totals
      vendorInfo.totalSpend += item.unitCost;
      vendorInfo.totalOrders++;
      if (date < vendorInfo.firstPurchase) vendorInfo.firstPurchase = date;
      if (date > vendorInfo.lastPurchase) vendorInfo.lastPurchase = date;
      
      // Track parts
      if (!vendorInfo.parts[part]) {
        vendorInfo.parts[part] = {
          partId: part,
          description: item.description || part,
          totalSpend: 0,
          totalOrders: 0,
          priceHistory: {},
          firstPrice: item.unitCost,
          lastPrice: item.unitCost,
          firstDate: date,
          lastDate: date,
          minPrice: item.unitCost,
          maxPrice: item.unitCost,
          avgPrice: 0
        };
        vendorInfo.partCount++;
      }
      
      const partInfo = vendorInfo.parts[part];
      partInfo.totalSpend += item.unitCost;
      partInfo.totalOrders++;
      if (date < partInfo.firstDate) {
        partInfo.firstDate = date;
        partInfo.firstPrice = item.unitCost;
      }
      if (date > partInfo.lastDate) {
        partInfo.lastDate = date;
        partInfo.lastPrice = item.unitCost;
      }
      partInfo.minPrice = Math.min(partInfo.minPrice, item.unitCost);
      partInfo.maxPrice = Math.max(partInfo.maxPrice, item.unitCost);
      
      // Track price history by year-month for both vendor and part
      if (!vendorInfo.priceHistory[yearMonth]) {
        vendorInfo.priceHistory[yearMonth] = {
          totalCost: 0,
          totalOrders: 0,
          avgPrice: 0,
          parts: {}
        };
      }
      
      if (!partInfo.priceHistory[yearMonth]) {
        partInfo.priceHistory[yearMonth] = {
          totalCost: 0,
          totalOrders: 0,
          avgPrice: 0,
          minPrice: item.unitCost,
          maxPrice: item.unitCost
        };
      }
      
      // Update monthly data
      const monthlyVendor = vendorInfo.priceHistory[yearMonth];
      const monthlyPart = partInfo.priceHistory[yearMonth];
      
      monthlyVendor.totalCost += item.unitCost;
      monthlyVendor.totalOrders++;
      monthlyVendor.parts[part] = (monthlyVendor.parts[part] || 0) + item.unitCost;
      
      monthlyPart.totalCost += item.unitCost;
      monthlyPart.totalOrders++;
      monthlyPart.minPrice = Math.min(monthlyPart.minPrice, item.unitCost);
      monthlyPart.maxPrice = Math.max(monthlyPart.maxPrice, item.unitCost);
    });

    // Calculate derived metrics
    this.vendorAnalysis = Object.values(vendorData).map(vendor => {
      // Calculate vendor averages
      vendor.avgOrderValue = vendor.totalSpend / Math.max(1, vendor.totalOrders);
      
      // Calculate monthly averages for vendor
      Object.keys(vendor.priceHistory).forEach(month => {
        const monthData = vendor.priceHistory[month];
        monthData.avgPrice = monthData.totalCost / Math.max(1, monthData.totalOrders);
      });
      
      // Calculate part averages and price changes
      Object.values(vendor.parts).forEach(part => {
        part.avgPrice = part.totalSpend / Math.max(1, part.totalOrders);
        
        // Calculate overall price change for part
        if (part.firstPrice > 0) {
          part.priceChangePercent = ((part.lastPrice - part.firstPrice) / part.firstPrice) * 100;
          part.priceChangeAmount = part.lastPrice - part.firstPrice;
        } else {
          part.priceChangePercent = 0;
          part.priceChangeAmount = 0;
        }
        
        // Calculate monthly averages for part
        Object.keys(part.priceHistory).forEach(month => {
          const monthData = part.priceHistory[month];
          monthData.avgPrice = monthData.totalCost / Math.max(1, monthData.totalOrders);
        });
        
        // Calculate price volatility (standard deviation of monthly prices)
        const monthlyPrices = Object.values(part.priceHistory).map(m => m.avgPrice);
        if (monthlyPrices.length > 1) {
          const mean = monthlyPrices.reduce((sum, price) => sum + price, 0) / monthlyPrices.length;
          const variance = monthlyPrices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / monthlyPrices.length;
          part.priceVolatility = Math.sqrt(variance);
        } else {
          part.priceVolatility = 0;
        }
      });
      
      // Calculate vendor overall price change (weighted by spend)
      let totalWeightedChange = 0;
      let totalWeight = 0;
      
      Object.values(vendor.parts).forEach(part => {
        const weight = part.totalSpend;
        totalWeightedChange += part.priceChangePercent * weight;
        totalWeight += weight;
      });
      
      vendor.overallPriceChange = totalWeight > 0 ? totalWeightedChange / totalWeight : 0;
      
      // Calculate vendor relationship duration
      const daysDiff = Math.floor((vendor.lastPurchase - vendor.firstPurchase) / (1000 * 60 * 60 * 24));
      vendor.relationshipDays = daysDiff;
      vendor.relationshipYears = Math.round((daysDiff / 365.25) * 10) / 10;
      
      // Sort parts by total spend
      vendor.sortedParts = Object.values(vendor.parts)
        .sort((a, b) => b.totalSpend - a.totalSpend);
      
      return vendor;
    });

    // Sort vendors by total spend by default
    this.vendorAnalysis.sort((a, b) => b.totalSpend - a.totalSpend);
  }

  applyFilters() {
    let vendors = [...this.vendorAnalysis];
    
    // Apply search filter
    if (this.searchTerm) {
      vendors = vendors.filter(vendor => 
        vendor.vendorName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    
    // Apply sorting
    vendors.sort((a, b) => {
      let aVal, bVal;
      
      switch (this.sortField) {
        case 'vendorName':
          aVal = a.vendorName;
          bVal = b.vendorName;
          break;
        case 'totalSpend':
          aVal = a.totalSpend;
          bVal = b.totalSpend;
          break;
        case 'partCount':
          aVal = a.partCount;
          bVal = b.partCount;
          break;
        case 'totalOrders':
          aVal = a.totalOrders;
          bVal = b.totalOrders;
          break;
        case 'overallPriceChange':
          aVal = a.overallPriceChange;
          bVal = b.overallPriceChange;
          break;
        case 'relationshipYears':
          aVal = a.relationshipYears;
          bVal = b.relationshipYears;
          break;
        default:
          aVal = a.totalSpend;
          bVal = b.totalSpend;
      }
      
      if (typeof aVal === 'string') {
        return this.sortDirection === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      } else {
        return this.sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
      }
    });
    
    this.filteredData = vendors;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredData.length / this.itemsPerPage));
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    // Check if container already has the modal, if not create it
    if (!this.container.querySelector('#vendor-detail-modal')) {
      this.container.innerHTML = `
        <div class="vendor-report-container">
          <div id="vendor-table-container"></div>
          ${this.renderModal()}
        </div>
      `;
      this.setupModalListeners();
    }
    
    // Only update the table content
    const tableContainer = this.container.querySelector('#vendor-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = this.renderTable();
      this.setupTableEventListeners();
    }
  }

  renderHeader() {
    // No header needed - using shared header from main component
    return '';
  }

  renderTable() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredData.slice(startIndex, endIndex);
    
    return `
      <!-- Vendor Analysis Table - Same styling as opportunity.js -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="vendorName">
                Vendor <span class="sort-indicator">${this.getSortIndicator('vendorName')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalSpend">
                Total Spend <span class="sort-indicator">${this.getSortIndicator('totalSpend')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="partCount">
                Parts Count <span class="sort-indicator">${this.getSortIndicator('partCount')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalOrders">
                Total Orders <span class="sort-indicator">${this.getSortIndicator('totalOrders')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="overallPriceChange">
                Price Change <span class="sort-indicator">${this.getSortIndicator('overallPriceChange')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Avg Order Value
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="relationshipYears">
                Relationship <span class="sort-indicator">${this.getSortIndicator('relationshipYears')}</span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${pageData.length > 0 ? pageData.map(vendor => this.renderVendorRow(vendor)).join('') : this.renderEmptyRow()}
          </tbody>
        </table>
      </div>

      <!-- Pagination - Exact copy of opportunity.js styling -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min(startIndex + 1, this.filteredData.length)} to 
          ${Math.min(endIndex, this.filteredData.length)} of 
          ${this.filteredData.length} vendors
        </div>
        
        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>
          
          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>
          
          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
  }

  getSortIndicator(field) {
    if (this.sortField === field) {
      return this.sortDirection === 'asc' ? '↑' : '↓';
    }
    return '';
  }

  renderEmptyRow() {
    return `
      <tr>
        <td colspan="8" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
          No vendors found matching your criteria
        </td>
      </tr>
    `;
  }

  renderVendorRow(vendor) {
    const priceChangeColor = vendor.overallPriceChange >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400';
    
    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(vendor.vendorName)}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Since ${vendor.firstPurchase.toLocaleDateString()}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${this.formatCurrency(vendor.totalSpend)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.partCount}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.totalOrders.toLocaleString()}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium ${priceChangeColor}">
            ${vendor.overallPriceChange >= 0 ? '+' : ''}${vendor.overallPriceChange.toFixed(1)}%
          </div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${this.formatCurrency(vendor.avgOrderValue)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.relationshipYears} years</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button class="view-vendor-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-vendor="${this.escapeHtml(vendor.vendorName)}" title="View Details">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `;
  }

  renderModal() {
    return `
      <!-- Vendor Detail Modal -->
      <div id="vendor-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-2 sm:p-4">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-5xl h-[90vh] flex flex-col">
            <!-- Fixed Header -->
            <div class="flex-shrink-0 px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-center min-h-[2rem]">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate pr-4" id="modal-vendor-title">Vendor Price Analysis</h3>
                <button id="close-vendor-modal" class="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            <!-- Scrollable Content -->
            <div id="modal-vendor-content" class="flex-1 p-4 sm:p-6 overflow-y-auto">
              <!-- Content will be populated dynamically -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Initial setup - called once
    this.render();
  }
  
  setupTableEventListeners() {
    // Called every time table is re-rendered
    // Table sorting
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'desc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination
    this.setupPaginationListeners();

    // Vendor detail buttons
    this.setupVendorActionListeners();
  }

  setupPaginationListeners() {
    const firstBtn = this.container.querySelector('#first-page');
    const prevBtn = this.container.querySelector('#prev-page');
    const nextBtn = this.container.querySelector('#next-page');
    const lastBtn = this.container.querySelector('#last-page');

    if (firstBtn && !firstBtn.hasAttribute('data-listener-added')) {
      firstBtn.setAttribute('data-listener-added', 'true');
      firstBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    if (prevBtn && !prevBtn.hasAttribute('data-listener-added')) {
      prevBtn.setAttribute('data-listener-added', 'true');
      prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    if (nextBtn && !nextBtn.hasAttribute('data-listener-added')) {
      nextBtn.setAttribute('data-listener-added', 'true');
      nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    if (lastBtn && !lastBtn.hasAttribute('data-listener-added')) {
      lastBtn.setAttribute('data-listener-added', 'true');
      lastBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }
  }

  setupVendorActionListeners() {
    const detailButtons = this.container.querySelectorAll('.view-vendor-details');
    detailButtons.forEach(btn => {
      if (!btn.hasAttribute('data-listener-added')) {
        btn.setAttribute('data-listener-added', 'true');
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          const vendorName = e.target.closest('button').getAttribute('data-vendor');
          this.showVendorDetailModal(vendorName);
        });
      }
    });
  }

  setupModalListeners() {
    const modal = this.container.querySelector('#vendor-detail-modal');
    const closeBtn = this.container.querySelector('#close-vendor-modal');

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
        }
      });
    }
  }

  showVendorDetailModal(vendorName) {
    const vendor = this.vendorAnalysis.find(v => v.vendorName === vendorName);
    if (!vendor) return;

    const modal = this.container.querySelector('#vendor-detail-modal');
    const title = this.container.querySelector('#modal-vendor-title');
    const content = this.container.querySelector('#modal-vendor-content');

    title.textContent = `${vendorName} - Detailed Price Analysis`;
    content.innerHTML = this.renderVendorDetailContent(vendor);

    modal.classList.remove('hidden');

    // Setup modal-specific event listeners
    this.setupModalContentListeners(vendor);
  }

  renderVendorDetailContent(vendor) {
    // Get price history timeline
    const priceHistory = Object.keys(vendor.priceHistory).sort();
    
    return `
      <div class="space-y-6">
        <!-- Vendor Summary Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${this.formatCurrency(vendor.totalSpend)}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Spend</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${vendor.partCount}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Parts</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${vendor.totalOrders}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Orders</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold ${vendor.overallPriceChange >= 0 ? 'text-red-600' : 'text-green-600'}">${vendor.overallPriceChange >= 0 ? '+' : ''}${vendor.overallPriceChange.toFixed(1)}%</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Price Change</div>
          </div>
        </div>

        <!-- Price History Timeline -->
        <div>
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Monthly Price History</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Month</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Orders</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Spend</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Avg Price</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Active Parts</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Monthly Change</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${priceHistory.map((month, index) => {
                  const monthData = vendor.priceHistory[month];
                  const prevMonth = index > 0 ? vendor.priceHistory[priceHistory[index - 1]] : null;
                  const monthlyChange = prevMonth ? ((monthData.avgPrice - prevMonth.avgPrice) / prevMonth.avgPrice) * 100 : 0;
                  
                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${month}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${monthData.totalOrders}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.totalCost)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.avgPrice)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${Object.keys(monthData.parts).length}</td>
                      <td class="px-3 py-4 text-sm font-medium ${monthlyChange >= 0 ? 'text-red-600' : 'text-green-600'}">
                        ${index > 0 ? `${monthlyChange >= 0 ? '+' : ''}${monthlyChange.toFixed(1)}%` : '—'}
                      </td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Parts Analysis -->
        <div>
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Parts Price Analysis (Top 15 by Spend)</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Part ID</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Description</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Spend</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Orders</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Price Range</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Price Change</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Volatility</th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${vendor.sortedParts.slice(0, 15).map(part => `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(part.partId)}</td>
                    <td class="px-3 py-4 text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(part.description)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.totalSpend)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${part.totalOrders}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.minPrice)} - ${this.formatCurrency(part.maxPrice)}</td>
                    <td class="px-3 py-4 text-sm font-medium ${part.priceChangePercent >= 0 ? 'text-red-600' : 'text-green-600'}">
                      ${part.priceChangePercent >= 0 ? '+' : ''}${part.priceChangePercent.toFixed(1)}%
                      <div class="text-xs text-gray-500">${part.priceChangeAmount >= 0 ? '+' : ''}${this.formatCurrency(part.priceChangeAmount)}</div>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.priceVolatility)}</td>
                    <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button class="view-part-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-part="${this.escapeHtml(part.partId)}" data-vendor="${this.escapeHtml(vendor.vendorName)}" title="View Part Details">
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  setupModalContentListeners(vendor) {
    // Setup part detail buttons
    const partDetailButtons = this.container.querySelectorAll('.view-part-details');
    partDetailButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = e.target.closest('button').getAttribute('data-part');
        const vendorName = e.target.closest('button').getAttribute('data-vendor');
        this.showPartDetailInModal(vendorName, partId);
      });
    });
  }

  showPartDetailInModal(vendorName, partId) {
    const vendor = this.vendorAnalysis.find(v => v.vendorName === vendorName);
    const part = vendor ? vendor.parts[partId] : null;
    
    if (!vendor || !part) return;

    // Get all PO data for this vendor and part
    const poData = this.priceData.filter(item => 
      item.vendorName === vendorName && item.inventoryId === partId
    ).sort((a, b) => new Date(a.purchaseDate) - new Date(b.purchaseDate));

    // Update the existing modal content to show part details
    const modal = this.container.querySelector('#vendor-detail-modal');
    const title = this.container.querySelector('#modal-vendor-title');
    const content = this.container.querySelector('#modal-vendor-content');

    title.innerHTML = `
      <div class="flex items-center gap-3 min-w-0">
        <button id="back-to-vendor" class="flex-shrink-0 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <span class="truncate">${this.escapeHtml(partId)} - Purchase Order History</span>
      </div>
    `;
    
    content.innerHTML = `
      <div class="mb-4">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Vendor: ${this.escapeHtml(vendorName)} | ${poData.length} purchase orders
        </p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Date</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">PO Number</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Unit Cost</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Quantity</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Cost</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Change</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${poData.map((po, index) => {
              const prevPrice = index > 0 ? poData[index - 1].unitCost : po.unitCost;
              const priceChange = prevPrice > 0 ? ((po.unitCost - prevPrice) / prevPrice) * 100 : 0;
              const totalCost = po.unitCost * (po.quantity || 1);
              
              return `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${new Date(po.purchaseDate).toLocaleDateString()}</td>
                  <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(po.orderNumber || 'N/A')}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(po.unitCost)}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${po.quantity || 1}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(totalCost)}</td>
                  <td class="px-3 py-4 text-sm font-medium ${priceChange >= 0 ? 'text-red-600' : 'text-green-600'}">
                    ${index > 0 ? `${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(1)}%` : '—'}
                    ${index > 0 ? `<div class="text-xs">${priceChange >= 0 ? '+' : ''}${this.formatCurrency(po.unitCost - prevPrice)}</div>` : ''}
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    // Setup back button
    const backBtn = content.parentElement.querySelector('#back-to-vendor');
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        this.showVendorDetailModal(vendorName);
      });
    }
  }

  renderNoDataState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Available</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">
          No price data is available to generate the vendor report. Please ensure price data is loaded in the main tracker first.
        </p>
      </div>
    `;
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    this.currentPage = 1;
    this.applyFilters();
    this.calculateTotalPages();
    this.render();
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processVendorAnalysis();
      this.applyFilters();
      this.calculateTotalPages();
      this.render();
    } else {
      this.renderNoDataState();
    }
  }

  // Utility methods
  getTotalParts() {
    return this.vendorAnalysis.reduce((total, vendor) => total + vendor.partCount, 0);
  }

  formatCurrency(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '—';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Price Tracker Vendor Report Component
// Detailed vendor price analysis with parts-based price tracking over time

export class PriceTrackerVendorReportComponent {
  constructor(container) {
    this.container = container;
    this.priceData = [];
    this.vendorAnalysis = [];
    this.filteredData = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'totalSpend';
    this.sortDirection = 'desc';
    this.selectedVendor = null;
    this.dateRange = null;
  }

  async init(priceData, dateRange) {
    console.log("Initializing Price Tracker Vendor Report");
    
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    
    if (this.priceData.length === 0) {
      this.renderNoDataState();
      return;
    }

    // Process data for vendor analysis
    this.processVendorAnalysis();
    this.applyFilters();
    this.calculateTotalPages();
    
    // Render the report
    this.render();
    
    // Setup event listeners
    this.setupEventListeners();
  }

  processVendorAnalysis() {
    console.log(`Processing ${this.priceData.length} price records for vendor analysis`);
    
    // Group by vendor for comprehensive analysis
    const vendorData = {};
    
    this.priceData.forEach(item => {
      const vendor = item.vendorName;
      const part = item.inventoryId;
      const date = new Date(item.purchaseDate);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 1-based month
      const yearMonth = `${year}-${month.toString().padStart(2, '0')}`;
      
      if (!vendorData[vendor]) {
        vendorData[vendor] = {
          vendorName: vendor,
          parts: {},
          totalSpend: 0,
          totalOrders: 0,
          partCount: 0,
          priceHistory: {},
          firstPurchase: date,
          lastPurchase: date,
          avgOrderValue: 0
        };
      }
      
      const vendorInfo = vendorData[vendor];
      
      // Update vendor totals
      vendorInfo.totalSpend += item.unitCost;
      vendorInfo.totalOrders++;
      if (date < vendorInfo.firstPurchase) vendorInfo.firstPurchase = date;
      if (date > vendorInfo.lastPurchase) vendorInfo.lastPurchase = date;
      
      // Track parts
      if (!vendorInfo.parts[part]) {
        vendorInfo.parts[part] = {
          partId: part,
          description: item.description || part,
          totalSpend: 0,
          totalOrders: 0,
          priceHistory: {},
          firstPrice: item.unitCost,
          lastPrice: item.unitCost,
          firstDate: date,
          lastDate: date,
          minPrice: item.unitCost,
          maxPrice: item.unitCost,
          avgPrice: 0
        };
        vendorInfo.partCount++;
      }
      
      const partInfo = vendorInfo.parts[part];
      partInfo.totalSpend += item.unitCost;
      partInfo.totalOrders++;
      if (date < partInfo.firstDate) {
        partInfo.firstDate = date;
        partInfo.firstPrice = item.unitCost;
      }
      if (date > partInfo.lastDate) {
        partInfo.lastDate = date;
        partInfo.lastPrice = item.unitCost;
      }
      partInfo.minPrice = Math.min(partInfo.minPrice, item.unitCost);
      partInfo.maxPrice = Math.max(partInfo.maxPrice, item.unitCost);
      
      // Track price history by year-month for both vendor and part
      if (!vendorInfo.priceHistory[yearMonth]) {
        vendorInfo.priceHistory[yearMonth] = {
          totalCost: 0,
          totalOrders: 0,
          avgPrice: 0,
          parts: {}
        };
      }
      
      if (!partInfo.priceHistory[yearMonth]) {
        partInfo.priceHistory[yearMonth] = {
          totalCost: 0,
          totalOrders: 0,
          avgPrice: 0,
          minPrice: item.unitCost,
          maxPrice: item.unitCost
        };
      }
      
      // Update monthly data
      const monthlyVendor = vendorInfo.priceHistory[yearMonth];
      const monthlyPart = partInfo.priceHistory[yearMonth];
      
      monthlyVendor.totalCost += item.unitCost;
      monthlyVendor.totalOrders++;
      monthlyVendor.parts[part] = (monthlyVendor.parts[part] || 0) + item.unitCost;
      
      monthlyPart.totalCost += item.unitCost;
      monthlyPart.totalOrders++;
      monthlyPart.minPrice = Math.min(monthlyPart.minPrice, item.unitCost);
      monthlyPart.maxPrice = Math.max(monthlyPart.maxPrice, item.unitCost);
    });

    // Calculate derived metrics
    this.vendorAnalysis = Object.values(vendorData).map(vendor => {
      // Calculate vendor averages
      vendor.avgOrderValue = vendor.totalSpend / Math.max(1, vendor.totalOrders);
      
      // Calculate monthly averages for vendor
      Object.keys(vendor.priceHistory).forEach(month => {
        const monthData = vendor.priceHistory[month];
        monthData.avgPrice = monthData.totalCost / Math.max(1, monthData.totalOrders);
      });
      
      // Calculate part averages and price changes
      Object.values(vendor.parts).forEach(part => {
        part.avgPrice = part.totalSpend / Math.max(1, part.totalOrders);
        
        // Calculate overall price change for part
        if (part.firstPrice > 0) {
          part.priceChangePercent = ((part.lastPrice - part.firstPrice) / part.firstPrice) * 100;
          part.priceChangeAmount = part.lastPrice - part.firstPrice;
        } else {
          part.priceChangePercent = 0;
          part.priceChangeAmount = 0;
        }
        
        // Calculate monthly averages for part
        Object.keys(part.priceHistory).forEach(month => {
          const monthData = part.priceHistory[month];
          monthData.avgPrice = monthData.totalCost / Math.max(1, monthData.totalOrders);
        });
        
        // Calculate price volatility (standard deviation of monthly prices)
        const monthlyPrices = Object.values(part.priceHistory).map(m => m.avgPrice);
        if (monthlyPrices.length > 1) {
          const mean = monthlyPrices.reduce((sum, price) => sum + price, 0) / monthlyPrices.length;
          const variance = monthlyPrices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / monthlyPrices.length;
          part.priceVolatility = Math.sqrt(variance);
        } else {
          part.priceVolatility = 0;
        }
      });
      
      // Calculate vendor overall price change (weighted by spend)
      let totalWeightedChange = 0;
      let totalWeight = 0;
      
      Object.values(vendor.parts).forEach(part => {
        const weight = part.totalSpend;
        totalWeightedChange += part.priceChangePercent * weight;
        totalWeight += weight;
      });
      
      vendor.overallPriceChange = totalWeight > 0 ? totalWeightedChange / totalWeight : 0;
      
      // Calculate vendor relationship duration
      const daysDiff = Math.floor((vendor.lastPurchase - vendor.firstPurchase) / (1000 * 60 * 60 * 24));
      vendor.relationshipDays = daysDiff;
      vendor.relationshipYears = Math.round((daysDiff / 365.25) * 10) / 10;
      
      // Sort parts by total spend
      vendor.sortedParts = Object.values(vendor.parts)
        .sort((a, b) => b.totalSpend - a.totalSpend);
      
      return vendor;
    });

    // Sort vendors by total spend by default
    this.vendorAnalysis.sort((a, b) => b.totalSpend - a.totalSpend);
  }

  applyFilters() {
    let vendors = [...this.vendorAnalysis];
    
    // Apply search filter
    if (this.searchTerm) {
      vendors = vendors.filter(vendor => 
        vendor.vendorName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    
    // Apply sorting
    vendors.sort((a, b) => {
      let aVal, bVal;
      
      switch (this.sortField) {
        case 'vendorName':
          aVal = a.vendorName;
          bVal = b.vendorName;
          break;
        case 'totalSpend':
          aVal = a.totalSpend;
          bVal = b.totalSpend;
          break;
        case 'partCount':
          aVal = a.partCount;
          bVal = b.partCount;
          break;
        case 'totalOrders':
          aVal = a.totalOrders;
          bVal = b.totalOrders;
          break;
        case 'overallPriceChange':
          aVal = a.overallPriceChange;
          bVal = b.overallPriceChange;
          break;
        case 'relationshipYears':
          aVal = a.relationshipYears;
          bVal = b.relationshipYears;
          break;
        default:
          aVal = a.totalSpend;
          bVal = b.totalSpend;
      }
      
      if (typeof aVal === 'string') {
        return this.sortDirection === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      } else {
        return this.sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
      }
    });
    
    this.filteredData = vendors;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredData.length / this.itemsPerPage));
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    // Check if container already has the modal, if not create it
    if (!this.container.querySelector('#vendor-detail-modal')) {
      this.container.innerHTML = `
        <div class="vendor-report-container">
          <div id="vendor-table-container"></div>
          ${this.renderModal()}
        </div>
      `;
      this.setupModalListeners();
    }
    
    // Only update the table content
    const tableContainer = this.container.querySelector('#vendor-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = this.renderTable();
      this.setupTableEventListeners();
    }
  }

  renderHeader() {
    // No header needed - using shared header from main component
    return '';
  }

  renderTable() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredData.slice(startIndex, endIndex);
    
    return `
      <!-- Vendor Analysis Table - Same styling as opportunity.js -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="vendorName">
                Vendor <span class="sort-indicator">${this.getSortIndicator('vendorName')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalSpend">
                Total Spend <span class="sort-indicator">${this.getSortIndicator('totalSpend')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="partCount">
                Parts Count <span class="sort-indicator">${this.getSortIndicator('partCount')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalOrders">
                Total Orders <span class="sort-indicator">${this.getSortIndicator('totalOrders')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="overallPriceChange">
                Price Change <span class="sort-indicator">${this.getSortIndicator('overallPriceChange')}</span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Avg Order Value
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="relationshipYears">
                Relationship <span class="sort-indicator">${this.getSortIndicator('relationshipYears')}</span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${pageData.length > 0 ? pageData.map(vendor => this.renderVendorRow(vendor)).join('') : this.renderEmptyRow()}
          </tbody>
        </table>
      </div>

      <!-- Pagination - Exact copy of opportunity.js styling -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min(startIndex + 1, this.filteredData.length)} to 
          ${Math.min(endIndex, this.filteredData.length)} of 
          ${this.filteredData.length} vendors
        </div>
        
        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>
          
          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>
          
          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
  }

  getSortIndicator(field) {
    if (this.sortField === field) {
      return this.sortDirection === 'asc' ? '↑' : '↓';
    }
    return '';
  }

  renderEmptyRow() {
    return `
      <tr>
        <td colspan="8" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
          No vendors found matching your criteria
        </td>
      </tr>
    `;
  }

  renderVendorRow(vendor) {
    const priceChangeColor = vendor.overallPriceChange >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400';
    
    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(vendor.vendorName)}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">Since ${vendor.firstPurchase.toLocaleDateString()}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${this.formatCurrency(vendor.totalSpend)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.partCount}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.totalOrders.toLocaleString()}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm font-medium ${priceChangeColor}">
            ${vendor.overallPriceChange >= 0 ? '+' : ''}${vendor.overallPriceChange.toFixed(1)}%
          </div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${this.formatCurrency(vendor.avgOrderValue)}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900 dark:text-white">${vendor.relationshipYears} years</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button class="view-vendor-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-vendor="${this.escapeHtml(vendor.vendorName)}" title="View Details">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `;
  }

  renderModal() {
    return `
      <!-- Vendor Detail Modal -->
      <div id="vendor-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[85vh] overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white" id="modal-vendor-title">Vendor Price Analysis</h3>
                <button id="close-vendor-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            <div id="modal-vendor-content" class="p-6 overflow-y-auto max-h-[calc(85vh-120px)]">
              <!-- Content will be populated dynamically -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Initial setup - called once
    this.render();
  }
  
  setupTableEventListeners() {
    // Called every time table is re-rendered
    // Table sorting
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'desc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination
    this.setupPaginationListeners();

    // Vendor detail buttons
    this.setupVendorActionListeners();
  }

  setupPaginationListeners() {
    const firstBtn = this.container.querySelector('#first-page');
    const prevBtn = this.container.querySelector('#prev-page');
    const nextBtn = this.container.querySelector('#next-page');
    const lastBtn = this.container.querySelector('#last-page');

    if (firstBtn && !firstBtn.hasAttribute('data-listener-added')) {
      firstBtn.setAttribute('data-listener-added', 'true');
      firstBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    if (prevBtn && !prevBtn.hasAttribute('data-listener-added')) {
      prevBtn.setAttribute('data-listener-added', 'true');
      prevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    if (nextBtn && !nextBtn.hasAttribute('data-listener-added')) {
      nextBtn.setAttribute('data-listener-added', 'true');
      nextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    if (lastBtn && !lastBtn.hasAttribute('data-listener-added')) {
      lastBtn.setAttribute('data-listener-added', 'true');
      lastBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }
  }

  setupVendorActionListeners() {
    const detailButtons = this.container.querySelectorAll('.view-vendor-details');
    detailButtons.forEach(btn => {
      if (!btn.hasAttribute('data-listener-added')) {
        btn.setAttribute('data-listener-added', 'true');
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          const vendorName = e.target.closest('button').getAttribute('data-vendor');
          this.showVendorDetailModal(vendorName);
        });
      }
    });
  }

  setupModalListeners() {
    const modal = this.container.querySelector('#vendor-detail-modal');
    const closeBtn = this.container.querySelector('#close-vendor-modal');

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
        }
      });
    }
  }

  showVendorDetailModal(vendorName) {
    const vendor = this.vendorAnalysis.find(v => v.vendorName === vendorName);
    if (!vendor) return;

    const modal = this.container.querySelector('#vendor-detail-modal');
    const title = this.container.querySelector('#modal-vendor-title');
    const content = this.container.querySelector('#modal-vendor-content');

    title.textContent = `${vendorName} - Detailed Price Analysis`;
    content.innerHTML = this.renderVendorDetailContent(vendor);

    modal.classList.remove('hidden');

    // Setup modal-specific event listeners
    this.setupModalContentListeners(vendor);
  }

  renderVendorDetailContent(vendor) {
    // Get price history timeline
    const priceHistory = Object.keys(vendor.priceHistory).sort();
    
    return `
      <div class="space-y-6">
        <!-- Vendor Summary Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${this.formatCurrency(vendor.totalSpend)}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Spend</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${vendor.partCount}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Parts</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">${vendor.totalOrders}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Orders</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold ${vendor.overallPriceChange >= 0 ? 'text-red-600' : 'text-green-600'}">${vendor.overallPriceChange >= 0 ? '+' : ''}${vendor.overallPriceChange.toFixed(1)}%</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Price Change</div>
          </div>
        </div>

        <!-- Price History Timeline -->
        <div>
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Monthly Price History</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Month</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Orders</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Spend</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Avg Price</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Active Parts</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Monthly Change</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${priceHistory.map((month, index) => {
                  const monthData = vendor.priceHistory[month];
                  const prevMonth = index > 0 ? vendor.priceHistory[priceHistory[index - 1]] : null;
                  const monthlyChange = prevMonth ? ((monthData.avgPrice - prevMonth.avgPrice) / prevMonth.avgPrice) * 100 : 0;
                  
                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${month}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${monthData.totalOrders}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.totalCost)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.avgPrice)}</td>
                      <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${Object.keys(monthData.parts).length}</td>
                      <td class="px-3 py-4 text-sm font-medium ${monthlyChange >= 0 ? 'text-red-600' : 'text-green-600'}">
                        ${index > 0 ? `${monthlyChange >= 0 ? '+' : ''}${monthlyChange.toFixed(1)}%` : '—'}
                      </td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Parts Analysis -->
        <div>
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Parts Price Analysis (Top 15 by Spend)</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Part ID</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Description</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Spend</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Orders</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Price Range</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Price Change</th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Volatility</th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${vendor.sortedParts.slice(0, 15).map(part => `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(part.partId)}</td>
                    <td class="px-3 py-4 text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(part.description)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.totalSpend)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${part.totalOrders}</td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.minPrice)} - ${this.formatCurrency(part.maxPrice)}</td>
                    <td class="px-3 py-4 text-sm font-medium ${part.priceChangePercent >= 0 ? 'text-red-600' : 'text-green-600'}">
                      ${part.priceChangePercent >= 0 ? '+' : ''}${part.priceChangePercent.toFixed(1)}%
                      <div class="text-xs text-gray-500">${part.priceChangeAmount >= 0 ? '+' : ''}${this.formatCurrency(part.priceChangeAmount)}</div>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(part.priceVolatility)}</td>
                    <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button class="view-part-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-part="${this.escapeHtml(part.partId)}" data-vendor="${this.escapeHtml(vendor.vendorName)}" title="View Part Details">
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  setupModalContentListeners(vendor) {
    // Setup part detail buttons
    const partDetailButtons = this.container.querySelectorAll('.view-part-details');
    partDetailButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = e.target.closest('button').getAttribute('data-part');
        const vendorName = e.target.closest('button').getAttribute('data-vendor');
        this.showPartDetailInModal(vendorName, partId);
      });
    });
  }

  showPartDetailInModal(vendorName, partId) {
    const vendor = this.vendorAnalysis.find(v => v.vendorName === vendorName);
    const part = vendor ? vendor.parts[partId] : null;
    
    if (!vendor || !part) return;

    // Get all PO data for this vendor and part
    const poData = this.priceData.filter(item => 
      item.vendorName === vendorName && item.inventoryId === partId
    ).sort((a, b) => new Date(a.purchaseDate) - new Date(b.purchaseDate));

    // Update the existing modal content to show part details
    const modal = this.container.querySelector('#vendor-detail-modal');
    const title = this.container.querySelector('#modal-vendor-title');
    const content = this.container.querySelector('#modal-vendor-content');

    title.innerHTML = `
      <div class="flex items-center gap-3">
        <button id="back-to-vendor" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <span>${this.escapeHtml(partId)} - Purchase Order History</span>
      </div>
    `;
    
    content.innerHTML = `
      <div class="mb-4">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Vendor: ${this.escapeHtml(vendorName)} | ${poData.length} purchase orders
        </p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Date</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">PO Number</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Unit Cost</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Quantity</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total Cost</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Change</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${poData.map((po, index) => {
              const prevPrice = index > 0 ? poData[index - 1].unitCost : po.unitCost;
              const priceChange = prevPrice > 0 ? ((po.unitCost - prevPrice) / prevPrice) * 100 : 0;
              const totalCost = po.unitCost * (po.quantity || 1);
              
              return `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${new Date(po.purchaseDate).toLocaleDateString()}</td>
                  <td class="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(po.orderNumber || 'N/A')}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(po.unitCost)}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${po.quantity || 1}</td>
                  <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">${this.formatCurrency(totalCost)}</td>
                  <td class="px-3 py-4 text-sm font-medium ${priceChange >= 0 ? 'text-red-600' : 'text-green-600'}">
                    ${index > 0 ? `${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(1)}%` : '—'}
                    ${index > 0 ? `<div class="text-xs">${priceChange >= 0 ? '+' : ''}${this.formatCurrency(po.unitCost - prevPrice)}</div>` : ''}
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    // Setup back button
    const backBtn = content.parentElement.querySelector('#back-to-vendor');
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        this.showVendorDetailModal(vendorName);
      });
    }
  }

  renderNoDataState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Available</h3>
        <p class="text-gray-600 dark:text-gray-400 text-center max-w-md">
          No price data is available to generate the vendor report. Please ensure price data is loaded in the main tracker first.
        </p>
      </div>
    `;
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    this.currentPage = 1;
    this.applyFilters();
    this.calculateTotalPages();
    this.render();
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processVendorAnalysis();
      this.applyFilters();
      this.calculateTotalPages();
      this.render();
    } else {
      this.renderNoDataState();
    }
  }

  // Utility methods
  getTotalParts() {
    return this.vendorAnalysis.reduce((total, vendor) => total + vendor.partCount, 0);
  }

  formatCurrency(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '—';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
